<?xml version="1.0"?>
<robot name="robotcar" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- XACRO参数：可以在launch文件中传递 -->
  <xacro:arg name="controllers_file" default=""/>

  <!-- 激光雷达动态参数 -->
  <xacro:arg name="front_right_laser_x" default="0.3499"/>
  <xacro:arg name="front_right_laser_y" default="-0.219"/>
  <xacro:arg name="front_right_laser_z" default="-0.25"/>
  <xacro:arg name="front_right_laser_roll" default="0.0"/>
  <xacro:arg name="front_right_laser_pitch" default="0.0"/>
  <xacro:arg name="front_right_laser_yaw" default="-0.6747"/>

  <xacro:arg name="rear_left_laser_x" default="-0.3499"/>
  <xacro:arg name="rear_left_laser_y" default="0.219"/>
  <xacro:arg name="rear_left_laser_z" default="-0.25"/>
  <xacro:arg name="rear_left_laser_roll" default="0.0"/>
  <xacro:arg name="rear_left_laser_pitch" default="0.0"/>
  <xacro:arg name="rear_left_laser_yaw" default="2.4669"/>

  <!-- 获取参数值 -->
  <xacro:property name="use_gazebo" value="$(arg use_gazebo)"/>
  <xacro:property name="controllers_file" value="$(arg controllers_file)"/>

  <!-- 激光雷达位置参数 -->
  <xacro:property name="front_right_laser_x" value="$(arg front_right_laser_x)"/>
  <xacro:property name="front_right_laser_y" value="$(arg front_right_laser_y)"/>
  <xacro:property name="front_right_laser_z" value="$(arg front_right_laser_z)"/>
  <xacro:property name="front_right_laser_roll" value="$(arg front_right_laser_roll)"/>
  <xacro:property name="front_right_laser_pitch" value="$(arg front_right_laser_pitch)"/>
  <xacro:property name="front_right_laser_yaw" value="$(arg front_right_laser_yaw)"/>

  <xacro:property name="rear_left_laser_x" value="$(arg rear_left_laser_x)"/>
  <xacro:property name="rear_left_laser_y" value="$(arg rear_left_laser_y)"/>
  <xacro:property name="rear_left_laser_z" value="$(arg rear_left_laser_z)"/>
  <xacro:property name="rear_left_laser_roll" value="$(arg rear_left_laser_roll)"/>
  <xacro:property name="rear_left_laser_pitch" value="$(arg rear_left_laser_pitch)"/>
  <xacro:property name="rear_left_laser_yaw" value="$(arg rear_left_laser_yaw)"/>

  <!-- ======================== 常用颜色 ======================== -->
  <material name="orange">
    <color rgba="1.0 0.5 0.2 1.0"/>
  </material>
  <material name="gray">
    <color rgba="0.5 0.5 0.5 1.0"/>
  </material>
  <material name="dark_gray">
    <color rgba="0.3 0.3 0.3 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.2 0.4 0.8 1.0"/>
  </material>
  <material name="black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.2 0.2 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.2 0.8 0.2 1.0"/>
  </material>
  <material name="white">
    <color rgba="0.9 0.9 0.9 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.7 0.7 0.7 1.0"/>
  </material>

  <!-- ======================== 几何/安装参数 ======================== -->
  <xacro:property name="M_PI" value="3.14159265359"/>

  <!-- 车体 -->
  <xacro:property name="vehicle_length" value="0.75"/>   <!-- x -->
  <xacro:property name="vehicle_width"  value="0.60"/>   <!-- y -->
  <xacro:property name="vehicle_height" value="0.80"/> <!-- z -->
  <xacro:property name="ground_clearance" value="0.047"/>

  <!-- 车体几何中心离地高度 -->
  <xacro:property name="base_box_z" value="${vehicle_height - ground_clearance}"/>
  <!-- {{ AURA-X: Fix - 设置base_link在轮子中心高度，确保旋转中心在base_footprint(地面). Approval: 寸止(ID:1735659900). }} -->
  <xacro:property name="base_link_z_offset" value="${ground_clearance}"/> 

  <!-- 轮子 -->
  <xacro:property name="wheel_radius" value="0.085"/>    <!-- 170 mm / 2 (原来的两倍) -->
  <xacro:property name="wheel_length" value="0.03"/>
  <!-- {{ AURA-X: Fix - 调整轮子Z偏移，确保轮子接触地面.  }} -->
  <xacro:property name="wheel_offset_z" value="${ground_clearance - base_link_z_offset}"/>  <!-- 轮子相对base_link的Z偏移 -->

  <!-- 轮子Y位置：确保轮子在车体内部，距离侧面10mm -->
  <xacro:property name="wheel_y_offset"
                  value="${vehicle_width/2 - wheel_length/2 - 0.01}"/>
  <!-- 实际轮距（两轮中心距离） -->
  <xacro:property name="wheel_separation" value="${2 * wheel_y_offset}"/>

  <!-- 万向轮 -->
  <xacro:property name="caster_radius" value="0.02"/>
  <xacro:property name="caster_length" value="0.015"/>
  <xacro:property name="caster_offset_from_edge" value="0.09"/>

  <!-- 单目摄像头：嵌入车体前部内侧，距前表面 20mm，距上表面 100mm -->
  <xacro:property name="cam_x" value="${vehicle_length/2 - 0.02}"/>
  <xacro:property name="cam_z" value="${base_box_z/2 - 0.10}"/>

  <!-- 雷达：对角安装，右前 & 左后 -->
  <xacro:property name="lidar_height_from_ground" value="0.255"/>
  <!-- 调整雷达Z偏移，适配新的base_link高度 }} -->
  <xacro:property name="lidar_offset_z" value="${lidar_height_from_ground - base_link_z_offset}"/>  <!-- 雷达相对base_link的Z偏移 -->
  <xacro:property name="lidar_cylinder_length" value="0.0"/>
  <xacro:property name="lidar_cylinder_radius" value="0.0"/>

  <!-- 雷达外围空圈 -->
  <xacro:property name="hollow_height" value="0.054"/>
  <xacro:property name="hollow_depth" value="0.075"/>
  <xacro:property name="hollow_outer_radius"
                  value="${lidar_cylinder_radius + hollow_depth}"/>
  <xacro:property name="hollow_inner_radius"
                  value="${lidar_cylinder_radius + 0.01}"/>

  <!-- ======================== 通用宏 ======================== -->
  <xacro:macro name="wheel_link"
               params="name parent_link x_offset y_offset z_offset">
    <link name="${name}_wheel_link">
      <visual>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="${wheel_length}" radius="${wheel_radius}"/>
        </geometry>
        <material name="black"/>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="${wheel_length}" radius="${wheel_radius}"/>
        </geometry>
      </collision>
      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <mass value="0.5"/>
        <inertia ixx="${0.5*(3*wheel_radius*wheel_radius + wheel_length*wheel_length)/12}"
                 ixy="0" ixz="0"
                 iyy="${0.5*(3*wheel_radius*wheel_radius + wheel_length*wheel_length)/12}"
                 iyz="0"
                 izz="${0.5*wheel_radius*wheel_radius/2}"/>
      </inertial>
    </link>

    <joint name="${name}_wheel_joint" type="continuous">
      <parent link="${parent_link}"/>
      <child  link="${name}_wheel_link"/>
      <origin xyz="${x_offset} ${y_offset} ${z_offset}" rpy="0 0 0"/>
      <axis xyz="0 1 0"/>
      <limit effort="10.0" velocity="10.0"/>
      <dynamics damping="0.1" friction="0.1"/>
    </joint>
  </xacro:macro>

  <xacro:macro name="caster_link" params="name parent_link x_sign y_sign">
    <link name="${name}_caster_link">
      <visual>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="${caster_length}" radius="${caster_radius}"/>
        </geometry>
        <material name="gray"/>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="${M_PI/2} 0 0"/>
        <geometry>
          <cylinder length="${caster_length}" radius="${caster_radius}"/>
        </geometry>
      </collision>
      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <mass value="0.1"/>
        <inertia ixx="${0.1*(3*caster_radius*caster_radius + caster_length*caster_length)/12}"
                 ixy="0" ixz="0"
                 iyy="${0.1*(3*caster_radius*caster_radius + caster_length*caster_length)/12}"
                 iyz="0"
                 izz="${0.1*caster_radius*caster_radius/2}"/>
      </inertial>
    </link>

    <joint name="${name}_caster_joint" type="fixed">
      <parent link="${parent_link}"/>
      <child  link="${name}_caster_link"/>
      <origin xyz="${x_sign*(vehicle_length/2-caster_offset_from_edge)}
                  ${y_sign*(vehicle_width/2-caster_offset_from_edge)}
                  ${wheel_offset_z}"
              rpy="0 0 0"/>
    </joint>
  </xacro:macro>

  <!-- ======================== 车体结构 ======================== -->
  <link name="base_footprint"/>
  <joint name="base_joint" type="fixed">
    <parent link="base_footprint"/>
    <child  link="base_link"/>
    <origin xyz="0 0 ${base_link_z_offset}" rpy="0 0 0"/>
  </joint>

  <link name="base_link">
    <visual>
      <geometry>
        <box size="${vehicle_length} ${vehicle_width} ${base_box_z}"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="${vehicle_length} ${vehicle_width} ${base_box_z}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="50"/>
      <inertia
        ixx="${(1/12)*50*(vehicle_width*vehicle_width + base_box_z*base_box_z)}"
        ixy="0" ixz="0"
        iyy="${(1/12)*50*(vehicle_length*vehicle_length + base_box_z*base_box_z)}"
        iyz="0"
        izz="${(1/12)*50*(vehicle_length*vehicle_length + vehicle_width*vehicle_width)}"/>
    </inertial>
  </link>

  <!-- ======================== 摄像头 ======================== -->
  <!-- 摄像头尺寸：长x宽x高 = 50mm x 40mm x 30mm，便于可视化 -->
  <xacro:property name="camera_length" value="0.05"/>
  <xacro:property name="camera_width" value="0.04"/>
  <xacro:property name="camera_height" value="0.03"/>

  <link name="camera_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry><box size="${camera_length} ${camera_width} ${camera_height}"/></geometry>
      <material name="red"/>  <!-- 改为红色，更容易看到 -->
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry><box size="${camera_length} ${camera_width} ${camera_height}"/></geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.05"/>
      <inertia ixx="${0.05*(camera_width*camera_width + camera_height*camera_height)/12}"
               ixy="0" ixz="0"
               iyy="${0.05*(camera_length*camera_length + camera_height*camera_height)/12}"
               iyz="0"
               izz="${0.05*(camera_length*camera_length + camera_width*camera_width)/12}"/>
    </inertial>
  </link>
  <joint name="camera_joint" type="fixed">
    <parent link="base_link"/>
    <child  link="camera_link"/>
    <origin xyz="${cam_x} 0 ${cam_z}" rpy="0 0 0"/>
  </joint>

  <!-- Camera optical frame (follows ROS convention: x-forward, y-left, z-up) -->
  <link name="camera_optical_frame"/>
  <joint name="camera_optical_joint" type="fixed">
    <parent link="camera_link"/>
    <child link="camera_optical_frame"/>
    <origin xyz="0 0 0" rpy="${-M_PI/2} 0 ${-M_PI/2}"/>
  </joint>

  <!-- ======================== IMU传感器 ======================== -->
  <!-- IMU安装在机器人中心位置，与base_link重合 -->
  <link name="imu_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.02 0.02 0.01"/>  <!-- 小盒子表示IMU -->
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.02 0.02 0.01"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6"/>
    </inertial>
  </link>

  <joint name="imu_joint" type="fixed">
    <parent link="base_link"/>
    <child link="imu_link"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>     <!-- {{ AURA-X: Fix - IMU安装在base_link中心位置，作为tracking frame. }} -->
  </joint>

  <!-- ======================== 雷达（对角安装 2 颗） ======================== -->
  <xacro:macro name="lidar_unit" params="prefix pos_x pos_y pos_z rot_r rot_p rot_y">
    <link name="${prefix}_laser">
      <visual>
        <geometry>
          <cylinder length="${lidar_cylinder_length}" radius="${lidar_cylinder_radius}"/>
        </geometry>
        <material name="red"/>
      </visual>
      <collision>
        <geometry>
          <cylinder length="${lidar_cylinder_length}" radius="${lidar_cylinder_radius}"/>
        </geometry>
      </collision>
      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <mass value="0.3"/>
        <inertia ixx="${0.3*(3*lidar_cylinder_radius*lidar_cylinder_radius + lidar_cylinder_length*lidar_cylinder_length)/12}"
                 ixy="0" ixz="0"
                 iyy="${0.3*(3*lidar_cylinder_radius*lidar_cylinder_radius + lidar_cylinder_length*lidar_cylinder_length)/12}"
                 iyz="0"
                 izz="${0.3*lidar_cylinder_radius*lidar_cylinder_radius/2}"/>
      </inertial>
    </link>
    <joint name="${prefix}_laser_joint" type="fixed">
      <parent link="base_link"/>
      <child  link="${prefix}_laser"/>
      <origin xyz="${pos_x} ${pos_y} ${pos_z}"
              rpy="${rot_r} ${rot_p} ${rot_y}"/>
    </joint>

    <!-- 雷达空圈 -->
    <link name="${prefix}_lidar_hollow_ring">
      <visual>
        <geometry>
          <cylinder length="${hollow_height}" radius="${hollow_outer_radius}"/>
        </geometry>
        <material name="dark_gray">
          <color rgba="0.3 0.3 0.3 0.8"/>
        </material>
      </visual>
      <inertial>
        <mass value="0.1"/>
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6"/>
      </inertial>
    </link>
    <joint name="${prefix}_lidar_hollow_joint" type="fixed">
      <parent link="base_link"/>
      <child  link="${prefix}_lidar_hollow_ring"/>
      <origin xyz="${pos_x} ${pos_y}
                   ${pos_z + lidar_cylinder_length/2 + hollow_height/2}"
              rpy="0 0 0"/>
    </joint>
  </xacro:macro>

  <!-- 2 颗对角雷达 - 使用动态参数 -->
  <xacro:lidar_unit prefix="front_right"
                    pos_x="${front_right_laser_x}"
                    pos_y="${front_right_laser_y}"
                    pos_z="${front_right_laser_z}"
                    rot_r="${front_right_laser_roll}"
                    rot_p="${front_right_laser_pitch}"
                    rot_y="${front_right_laser_yaw}"/>
  <xacro:lidar_unit prefix="rear_left"
                    pos_x="${rear_left_laser_x}"
                    pos_y="${rear_left_laser_y}"
                    pos_z="${rear_left_laser_z}"
                    rot_r="${rear_left_laser_roll}"
                    rot_p="${rear_left_laser_pitch}"
                    rot_y="${rear_left_laser_yaw}"/>

  <!-- ======================== 万向轮（四角对称） ======================== -->
  <xacro:caster_link name="fl_caster" parent_link="base_link"
                     x_sign="1"  y_sign="1"/>
  <xacro:caster_link name="fr_caster" parent_link="base_link"
                     x_sign="1"  y_sign="-1"/>
  <xacro:caster_link name="rl_caster" parent_link="base_link"
                     x_sign="-1" y_sign="1"/>
  <xacro:caster_link name="rr_caster" parent_link="base_link"
                     x_sign="-1" y_sign="-1"/>

  <!-- ======================== 驱动轮（左右中置） ======================== -->
  <xacro:wheel_link name="left"  parent_link="base_link"
                    x_offset="0"  y_offset="${wheel_y_offset}"  z_offset="${wheel_offset_z}"/>
  <xacro:wheel_link name="right" parent_link="base_link"
                    x_offset="0"  y_offset="-${wheel_y_offset}" z_offset="${wheel_offset_z}"/>

  <!-- ======================== ros2_control 配置 ======================== -->
  <ros2_control name="RobotCar" type="system">
    <hardware>
      <plugin>robotcar_base/RobotCarHardwareInterface</plugin>
      <param name="serial_port_name">/dev/STM32</param>
      <param name="serial_baud_rate">115200</param>
      <param name="wheel_radius">${wheel_radius}</param>
      <param name="wheel_separation">${wheel_separation}</param>
      <param name="loop_rate">50</param>
      <param name="device_timeout">1000</param>
      <!-- IMU传感器参数 -->
      <param name="imu_port">/dev/imu_usb</param>
      <param name="imu_baud_rate">115200</param>
      <param name="imu_frame_id">imu_link</param>
    </hardware>
    <joint name="left_wheel_joint">
      <command_interface name="velocity">
        <param name="min">-10.0</param>
        <param name="max">10.0</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
    <joint name="right_wheel_joint">
      <command_interface name="velocity">
        <param name="min">-10.0</param>
        <param name="max">10.0</param>
      </command_interface>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
    </joint>
    <!-- IMU传感器状态接口 -->
    <sensor name="imu_sensor">
      <state_interface name="orientation.x"/>
      <state_interface name="orientation.y"/>
      <state_interface name="orientation.z"/>
      <state_interface name="orientation.w"/>
      <state_interface name="angular_velocity.x"/>
      <state_interface name="angular_velocity.y"/>
      <state_interface name="angular_velocity.z"/>
      <state_interface name="linear_acceleration.x"/>
      <state_interface name="linear_acceleration.y"/>
      <state_interface name="linear_acceleration.z"/>
      <param name="frame_id">imu_link</param>
    </sensor>
  </ros2_control>
</robot>